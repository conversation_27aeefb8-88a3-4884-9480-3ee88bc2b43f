/**
 * Simple test to verify session creation and file writing
 */

import { SessionManager } from './dist/core/sessionManager.js';
import { HumanMessage, AIMessage } from '@langchain/core/messages';
import path from 'path';
import { promises as fs } from 'fs';
import os from 'os';

async function testSimple() {
  console.log('🧪 Testing simple session functionality...');
  
  try {
    // Create a session manager with persistence enabled (default)
    console.log('Creating SessionManager...');
    const sessionManager = new SessionManager();
    
    // Wait a bit for async initialization
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Create a new session
    console.log('Creating session...');
    const sessionId = sessionManager.createSession('Test user memory', { testKey: 'testValue' });
    console.log(`✅ Created session: ${sessionId}`);
    
    // Wait a bit more for logger initialization
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Add some messages
    console.log('Adding messages...');
    const userMessage = new HumanMessage('Hello, this is a test message');
    const aiMessage = new AIMessage('Hi! This is a test response from the AI');
    
    sessionManager.updateSession(sessionId, [userMessage]);
    await new Promise(resolve => setTimeout(resolve, 500));
    
    sessionManager.updateSession(sessionId, [aiMessage]);
    await new Promise(resolve => setTimeout(resolve, 500));
    
    console.log('✅ Added messages to session');
    
    // Wait for async operations to complete
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Check if session directory exists
    const haicodeDir = path.join(os.homedir(), '.haicode');
    try {
      const stats = await fs.stat(haicodeDir);
      if (stats.isDirectory()) {
        console.log(`✅ .haicode directory exists at: ${haicodeDir}`);
        
        // List contents recursively
        const listDirectory = async (dir, prefix = '') => {
          const items = await fs.readdir(dir);
          for (const item of items) {
            const fullPath = path.join(dir, item);
            const stat = await fs.stat(fullPath);
            console.log(`${prefix}${item}${stat.isDirectory() ? '/' : ''}`);
            if (stat.isDirectory()) {
              await listDirectory(fullPath, prefix + '  ');
            }
          }
        };
        
        console.log('📁 .haicode directory contents:');
        await listDirectory(haicodeDir, '  ');
      }
    } catch (error) {
      console.log(`❌ .haicode directory not found: ${error.message}`);
    }
    
    // Test session retrieval
    const session = sessionManager.getSession(sessionId);
    if (session && session.messages.length >= 2) {
      console.log(`✅ Session retrieved with ${session.messages.length} messages`);
    } else {
      console.log(`❌ Session retrieval failed or incorrect message count: ${session?.messages.length || 0}`);
    }
    
    console.log('\n🎉 Test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testSimple().catch(console.error);