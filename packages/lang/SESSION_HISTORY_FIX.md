# 会话历史重复记录问题修复报告

## 问题描述

在 `packages/lang` 中维护会话历史时，存在会话记录重复记录的问题。经过分析发现主要有以下几个问题：

1. **`updateSessionFromAgentState` 方法缺少重复检测**：直接追加消息而不检查是否已存在
2. **`HaicodeLogger` 缺少重复检测机制**：与 `packages/core` 中的 Logger 不同，没有重复检测
3. **会话更新时的双重记录**：在 `updateSession` 方法中可能导致重复记录

## 修复方案

### 1. 改进 `updateSessionFromAgentState` 方法

**文件**: `packages/lang/src/core/sessionManager.ts`

**修改内容**:
- 将直接追加消息的逻辑改为调用 `updateSession` 方法
- 利用 `updateSession` 中的重复检测机制避免重复

```typescript
// 修改前：直接追加消息
session.messages.push(...newMessages);

// 修改后：使用 updateSession 方法
return this.updateSession(sessionId, newMessages, state.userMemory);
```

### 2. 添加消息重复检测机制

**文件**: `packages/lang/src/core/sessionManager.ts`

**新增功能**:
- 添加 `messageExists` 私有方法，检查消息是否已存在
- 支持不同类型消息的智能比较：
  - `HumanMessage` 和 `AIMessage`：比较内容
  - `AIMessage`：额外比较 tool_calls
  - `ToolMessage`：比较 tool_call_id

```typescript
private messageExists(session: SessionData, message: BaseMessage): boolean {
  return session.messages.some(existingMessage => {
    // 比较消息类型和内容
    // 对于 AIMessage 还比较 tool_calls
    // 对于 ToolMessage 比较 tool_call_id
  });
}
```

### 3. 改进 `updateSession` 方法

**文件**: `packages/lang/src/core/sessionManager.ts`

**修改内容**:
- 在添加消息前过滤重复消息
- 只记录和保存真正的新消息
- 改进日志输出，显示过滤的重复消息数量

```typescript
// 过滤重复消息
const uniqueNewMessages = newMessages.filter(message => !this.messageExists(session, message));

// 只处理唯一的新消息
if (uniqueNewMessages.length > 0) {
  session.messages.push(...uniqueNewMessages);
  // 记录日志...
}
```

### 4. 改进 `HaicodeLogger` 重复检测

**文件**: `packages/lang/src/core/logger.ts`

**修改内容**:
- 参考 `packages/core` 中的 Logger 实现，添加重复检测
- 检查相同会话、相同类型、相同内容和相同元数据的条目
- 跳过重复条目并返回 null

```typescript
// 检查重复条目
const entryExists = currentLogsOnDisk.some(
  (e) =>
    e.sessionId === entryToAppend.sessionId &&
    e.type === entryToAppend.type &&
    e.message === entryToAppend.message &&
    JSON.stringify(e.metadata || {}) === JSON.stringify(entryToAppend.metadata || {}),
);

if (entryExists) {
  log.debug(`Duplicate log entry detected and skipped`);
  return null;
}
```

## 测试验证

### 测试1：会话消息重复检测

创建了 `test-session-fix.js` 测试文件，验证：

1. ✅ 重复添加相同消息被正确过滤
2. ✅ `updateSessionFromAgentState` 正确处理重复消息
3. ✅ 不同类型消息（HumanMessage、AIMessage、ToolMessage）的重复检测
4. ✅ 带 tool_calls 的 AIMessage 重复检测

**测试结果**:
- 第一次添加2条消息：会话消息数量 = 2
- 重复添加相同消息：会话消息数量 = 2（未增加）
- 添加3条新消息：会话消息数量 = 5
- 重复添加部分消息：会话消息数量 = 5（未增加）

### 测试2：日志记录重复检测

创建了 `test-logger-fix.js` 测试文件，验证：

1. ✅ 相同内容的日志条目重复检测
2. ✅ 带元数据的消息重复检测
3. ✅ 不同消息正常记录

**测试结果**:
- 重复记录相同消息：日志条目数量不增加
- 重复记录带元数据消息：日志条目数量不增加
- 记录不同消息：正常增加日志条目

## 修复效果

1. **消除重复记录**：会话历史中不再出现重复的消息记录
2. **提高性能**：减少不必要的存储和处理
3. **保持一致性**：与 `packages/core` 中的逻辑保持一致
4. **向后兼容**：不影响现有功能，只是增强了重复检测

## 相关文件

- `packages/lang/src/core/sessionManager.ts` - 主要修复文件
- `packages/lang/src/core/logger.ts` - 日志重复检测修复
- `packages/lang/test-session-fix.js` - 会话测试文件
- `packages/lang/test-logger-fix.js` - 日志测试文件

## 注意事项

1. 修复后需要重新构建项目：`npm run build`
2. 重复检测基于消息内容、类型和元数据的比较
3. 对于 AIMessage，会比较 tool_calls 以确保准确性
4. 日志记录器的重复检测不依赖时间戳，而是基于内容相似性
