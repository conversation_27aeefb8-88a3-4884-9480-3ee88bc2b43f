/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { BaseMessage, HumanMessage, AIMessage, ToolMessage } from '@langchain/core/messages';
import { logger } from '../utils/logger.js';
import { v4 as uuidv4 } from 'uuid';
import { HaicodeLogger, MessageSenderType } from './logger.js';
import { promises as fs } from 'node:fs';
import path from 'node:path';
import { getProjectTempDir } from '../utils/paths.js';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc.js';
import timezone from 'dayjs/plugin/timezone.js';

import type { AgentState } from '../types/index.js';

// 配置 dayjs 插件
dayjs.extend(utc);
dayjs.extend(timezone);

/**
 * Session data interface
 */
export interface SessionData {
  id: string;
  createdAt: Date;
  lastActivity: Date;
  messages: BaseMessage[];
  userMemory?: string;
  metadata: Record<string, unknown>;
}

/**
 * Session manager that handles conversation persistence and memory
 */
export class SessionManager {
  private sessions: Map<string, SessionData> = new Map();
  private maxSessions: number;
  private sessionTimeout: number; // in milliseconds
  private loggers: Map<string, HaicodeLogger> = new Map();
  private persistenceEnabled: boolean;
  private sessionsFilePath: string;

  constructor(
    maxSessions = 100,
    sessionTimeout = 24 * 60 * 60 * 1000, // 24 hours
    persistenceEnabled = true,
    targetDir?: string
  ) {
    this.maxSessions = maxSessions;
    this.sessionTimeout = sessionTimeout;
    this.persistenceEnabled = persistenceEnabled;

    // Set up persistence file path using targetDir or current working directory
    const projectRoot = targetDir || process.cwd();
    const projectTempDir = getProjectTempDir(projectRoot);
    this.sessionsFilePath = path.join(projectTempDir, 'sessions.json');
    
    // Start cleanup interval
    this.startCleanupInterval();
    
    // Load existing sessions if persistence is enabled
    if (this.persistenceEnabled) {
      this.loadSessionsFromDisk().catch(error => {
        logger.error('[SessionManager] Failed to load sessions from disk:', error);
      });
    }
  }

  /**
   * Create a new session
   */
  createSession(userMemory?: string, metadata?: Record<string, unknown>): string {
    const sessionId = uuidv4();
    const now = new Date();
    
    const session: SessionData = {
      id: sessionId,
      createdAt: now,
      lastActivity: now,
      messages: [],
      userMemory,
      metadata: metadata || {},
    };
    
    this.sessions.set(sessionId, session);
    
    // Create and initialize logger for this session
    if (this.persistenceEnabled) {
      const sessionLogger = new HaicodeLogger(sessionId);
      // Initialize logger synchronously to ensure it's ready before use
      sessionLogger.initialize().then(() => {
        logger.debug(`[SessionManager] Logger initialized for session ${sessionId}`);
      }).catch(error => {
        logger.error(`[SessionManager] Failed to initialize logger for session ${sessionId}:`, error);
      });
      this.loggers.set(sessionId, sessionLogger);
    }
    
    // Clean up old sessions if we exceed the limit
    this.cleanupOldSessions();
    
    // Save to disk if persistence is enabled
    if (this.persistenceEnabled) {
      this.saveSessionsToDisk().catch(error => {
        logger.error('[SessionManager] Failed to save sessions to disk:', error);
      });
    }
    
    logger.debug(`[SessionManager] Created session: ${sessionId}`);
    return sessionId;
  }

  /**
   * Get an existing session
   */
  getSession(sessionId: string): SessionData | undefined {
    const session = this.sessions.get(sessionId);
    if (session) {
      session.lastActivity = new Date();
    }
    return session;
  }

  /**
   * Check if a message already exists in the session
   */
  private messageExists(session: SessionData, message: BaseMessage): boolean {
    return session.messages.some(existingMessage => {
      // Compare message type and content
      if (existingMessage.constructor.name !== message.constructor.name) {
        return false;
      }

      // Compare content
      if (String(existingMessage.content) !== String(message.content)) {
        return false;
      }

      // For AIMessage, also compare tool calls if present
      if (message.constructor.name === 'AIMessage') {
        const existingAI = existingMessage as AIMessage;
        const newAI = message as AIMessage;

        const existingToolCalls = existingAI.tool_calls || [];
        const newToolCalls = newAI.tool_calls || [];

        if (existingToolCalls.length !== newToolCalls.length) {
          return false;
        }

        // Compare tool calls
        for (let i = 0; i < existingToolCalls.length; i++) {
          const existing = existingToolCalls[i];
          const newCall = newToolCalls[i];
          if (existing.name !== newCall.name ||
              JSON.stringify(existing.args) !== JSON.stringify(newCall.args)) {
            return false;
          }
        }
      }

      // For ToolMessage, compare tool_call_id
      if (message.constructor.name === 'ToolMessage') {
        const existingTool = existingMessage as ToolMessage;
        const newTool = message as ToolMessage;
        return existingTool.tool_call_id === newTool.tool_call_id;
      }

      return true;
    });
  }

  /**
   * Update session with new messages
   */
  updateSession(
    sessionId: string,
    newMessages: BaseMessage[],
    userMemory?: string
  ): boolean {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return false;
    }

    // Filter out messages that already exist in the session
    const uniqueNewMessages = newMessages.filter(message => !this.messageExists(session, message));

    if (uniqueNewMessages.length === 0) {
      logger.debug(`[SessionManager] No new unique messages to add to session ${sessionId}`);
      // Still update user memory and last activity if provided
      if (userMemory !== undefined) {
        session.userMemory = userMemory;
        session.lastActivity = new Date();
      }
      return true;
    }

    session.messages.push(...uniqueNewMessages);
    session.lastActivity = new Date();

    if (userMemory !== undefined) {
      session.userMemory = userMemory;
    }
    
    // Log messages if persistence is enabled
    if (this.persistenceEnabled && this.loggers.has(sessionId)) {
      const sessionLogger = this.loggers.get(sessionId)!;
      uniqueNewMessages.forEach(message => {
        let messageType: MessageSenderType;
        let logContent: string;
        let metadata: Record<string, unknown> | undefined;

        if (message.constructor.name === 'HumanMessage') {
          messageType = MessageSenderType.USER;
          logContent = String(message.content);
        } else if (message.constructor.name === 'AIMessage') {
          messageType = MessageSenderType.ASSISTANT;
          const aiMessage = message as AIMessage;

          // Handle AIMessage with tool calls
          if (aiMessage.tool_calls && aiMessage.tool_calls.length > 0) {
            logContent = String(aiMessage.content || '');
            metadata = {
              tool_calls: aiMessage.tool_calls.map(tc => ({
                id: tc.id,
                name: tc.name,
                args: tc.args
              }))
            };
            // Add tool call information to log content for readability
            const toolCallsInfo = aiMessage.tool_calls.map(tc =>
              `[工具调用: ${tc.name}(${JSON.stringify(tc.args)})]`
            ).join(' ');
            logContent = logContent ? `${logContent} ${toolCallsInfo}` : toolCallsInfo;
          } else {
            logContent = String(aiMessage.content);
          }
        } else if (message.constructor.name === 'ToolMessage') {
          messageType = MessageSenderType.ASSISTANT;
          const toolMessage = message as ToolMessage;
          logContent = `[工具结果] ${String(toolMessage.content)}`;
          metadata = {
            tool_call_id: toolMessage.tool_call_id,
            message_type: 'tool_result'
          };
        } else {
          // Fallback for other message types
          messageType = MessageSenderType.ASSISTANT;
          logContent = String(message.content);
        }

        // Log immediately - the logger should be initialized by now
        sessionLogger.log(logContent, messageType, metadata).catch(error => {
          logger.error('[SessionManager] Failed to log message:', error);
        });
      });
    }
    
    // Save to disk if persistence is enabled
    if (this.persistenceEnabled) {
      this.saveSessionsToDisk().catch(error => {
        logger.error('[SessionManager] Failed to save sessions to disk:', error);
      });
    }
    
    logger.debug(`[SessionManager] Updated session ${sessionId} with ${uniqueNewMessages.length} new messages (${newMessages.length} total provided, ${newMessages.length - uniqueNewMessages.length} duplicates filtered)`);
    return true;
  }

  /**
   * Add a single message to session
   */
  addMessage(sessionId: string, message: BaseMessage): boolean {
    return this.updateSession(sessionId, [message]);
  }

  /**
   * Get conversation history for a session
   */
  getConversationHistory(sessionId: string): BaseMessage[] {
    const session = this.sessions.get(sessionId);
    return session ? [...session.messages] : [];
  }

  /**
   * Get user memory for a session
   */
  getUserMemory(sessionId: string): string | undefined {
    const session = this.sessions.get(sessionId);
    return session?.userMemory;
  }

  /**
   * Update user memory for a session
   */
  updateUserMemory(sessionId: string, userMemory: string): boolean {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return false;
    }
    
    session.userMemory = userMemory;
    session.lastActivity = new Date();
    
    logger.debug(`[SessionManager] Updated user memory for session ${sessionId}`);
    return true;
  }

  /**
   * Delete a session
   */
  deleteSession(sessionId: string): boolean {
    const deleted = this.sessions.delete(sessionId);
    if (deleted) {
      logger.debug(`[SessionManager] Deleted session: ${sessionId}`);
    }
    return deleted;
  }

  /**
   * Get all active session IDs
   */
  getActiveSessions(): string[] {
    return Array.from(this.sessions.keys());
  }

  /**
   * Get session count
   */
  getSessionCount(): number {
    return this.sessions.size;
  }

  /**
   * Get session metadata
   */
  getSessionMetadata(sessionId: string): Record<string, unknown> | undefined {
    const session = this.sessions.get(sessionId);
    return session?.metadata;
  }

  /**
   * Update session metadata
   */
  updateSessionMetadata(
    sessionId: string, 
    metadata: Record<string, unknown>
  ): boolean {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return false;
    }
    
    session.metadata = { ...session.metadata, ...metadata };
    session.lastActivity = new Date();
    
    return true;
  }

  /**
   * Get session statistics
   */
  getSessionStats(sessionId: string): {
    messageCount: number;
    userMessageCount: number;
    aiMessageCount: number;
    sessionDuration: number;
    lastActivity: Date;
  } | undefined {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return undefined;
    }
    
    const userMessageCount = session.messages.filter(m => m instanceof HumanMessage).length;
    const aiMessageCount = session.messages.filter(m => m instanceof AIMessage).length;
    const sessionDuration = session.lastActivity.getTime() - session.createdAt.getTime();
    
    return {
      messageCount: session.messages.length,
      userMessageCount,
      aiMessageCount,
      sessionDuration,
      lastActivity: session.lastActivity,
    };
  }

  /**
   * Convert session to AgentState
   */
  sessionToAgentState(sessionId: string): AgentState | undefined {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return undefined;
    }
    
    return {
      messages: [...session.messages],
      sessionId: session.id,
      userMemory: session.userMemory,
      isComplete: false,
      toolResults: [],
    };
  }

  /**
   * Update session from AgentState
   */
  updateSessionFromAgentState(sessionId: string, state: AgentState): boolean {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return false;
    }

    // Update messages (append new ones only)
    const existingMessageCount = session.messages.length;
    const newMessages = state.messages.slice(existingMessageCount);

    if (newMessages.length > 0) {
      // Use updateSession to properly handle logging and avoid duplication
      return this.updateSession(sessionId, newMessages, state.userMemory);
    }

    // Update user memory even if no new messages
    if (state.userMemory !== undefined) {
      session.userMemory = state.userMemory;
      session.lastActivity = new Date();
    }

    return true;
  }

  /**
   * Clean up expired sessions
   */
  private cleanupExpiredSessions(): void {
    const now = Date.now();
    const expiredSessions: string[] = [];
    
    for (const [sessionId, session] of this.sessions.entries()) {
      if (now - session.lastActivity.getTime() > this.sessionTimeout) {
        expiredSessions.push(sessionId);
      }
    }
    
    for (const sessionId of expiredSessions) {
      this.sessions.delete(sessionId);
      logger.debug(`[SessionManager] Expired session: ${sessionId}`);
    }
    
    if (expiredSessions.length > 0) {
      logger.debug(`[SessionManager] Cleaned up ${expiredSessions.length} expired sessions`);
    }
  }

  /**
   * Clean up old sessions if we exceed the limit
   */
  private cleanupOldSessions(): void {
    if (this.sessions.size <= this.maxSessions) {
      return;
    }
    
    // Sort sessions by last activity (oldest first)
    const sortedSessions = Array.from(this.sessions.entries())
      .sort(([, a], [, b]) => a.lastActivity.getTime() - b.lastActivity.getTime());
    
    const toDelete = this.sessions.size - this.maxSessions;
    const deletedSessions: string[] = [];
    
    for (let i = 0; i < toDelete; i++) {
      const [sessionId] = sortedSessions[i];
      this.sessions.delete(sessionId);
      deletedSessions.push(sessionId);
    }
    
    logger.debug(`[SessionManager] Cleaned up ${deletedSessions.length} old sessions to maintain limit`);
  }

  /**
   * Start automatic cleanup interval
   */
  private startCleanupInterval(): void {
    setInterval(() => {
      this.cleanupExpiredSessions();
    }, 60 * 60 * 1000); // Run every hour
  }

  /**
   * Export session data (for persistence)
   */
  exportSession(sessionId: string): string | undefined {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return undefined;
    }
    
    // Convert messages to serializable format
    const serializedSession = {
      ...session,
      createdAt: this.formatDateForChina(session.createdAt),
      lastActivity: this.formatDateForChina(session.lastActivity),
      messages: session.messages.map(msg => ({
        type: msg.constructor.name,
        content: msg.content,
        additional_kwargs: msg.additional_kwargs,
      })),
    };
    
    return JSON.stringify(serializedSession, null, 2);
  }

  /**
   * Import session data (from persistence)
   */
  importSession(sessionData: string): string | undefined {
    try {
      const parsed = JSON.parse(sessionData);
      
      // Reconstruct messages including tool messages
      const messages: BaseMessage[] = parsed.messages.map((msgData: Record<string, unknown>) => {
        switch (msgData.type) {
          case 'HumanMessage':
            return new HumanMessage(String(msgData.content));
          case 'AIMessage': {
            // Reconstruct AIMessage with tool calls if present
            const aiMessage = new AIMessage(String(msgData.content));
            if (msgData.tool_calls && Array.isArray(msgData.tool_calls)) {
              aiMessage.tool_calls = msgData.tool_calls as Array<{
                id: string;
                name: string;
                args: Record<string, unknown>;
              }>;
            }
            return aiMessage;
          }
          case 'ToolMessage':
            // Reconstruct ToolMessage
            return new ToolMessage({
              content: String(msgData.content),
              tool_call_id: String(msgData.tool_call_id || ''),
            });
          default:
            return new HumanMessage(String(msgData.content)); // fallback
        }
      });
      
      const session: SessionData = {
        id: parsed.id,
        createdAt: this.parseDateFromString(parsed.createdAt),
        lastActivity: this.parseDateFromString(parsed.lastActivity),
        messages,
        userMemory: parsed.userMemory,
        metadata: parsed.metadata || {},
      };
      
      this.sessions.set(session.id, session);
      
      logger.debug(`[SessionManager] Imported session: ${session.id}`);
      return session.id;
    } catch (error) {
      logger.error('[SessionManager] Failed to import session:', error);
      return undefined;
    }
  }

  /**
   * Clear all sessions
   */
  clearAllSessions(): void {
    const count = this.sessions.size;
    this.sessions.clear();
    this.loggers.clear();
    
    // Clear sessions file if persistence is enabled
    if (this.persistenceEnabled) {
      this.saveSessionsToDisk().catch(error => {
        logger.error('[SessionManager] Failed to clear sessions file:', error);
      });
    }
    
    logger.debug(`[SessionManager] Cleared ${count} sessions`);
  }

  /**
   * 将日期格式化为中国时区显示格式
   */
  private formatDateForChina(date: Date): string {
    return dayjs(date).tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss') + ' (中国时间)';
  }

  /**
   * Save sessions to disk
   */
  private async saveSessionsToDisk(): Promise<void> {
    if (!this.persistenceEnabled) {
      return;
    }

    try {
      // Ensure directory exists
      await fs.mkdir(path.dirname(this.sessionsFilePath), { recursive: true });
      
      // Convert sessions to serializable format
      const sessionsData = Array.from(this.sessions.entries()).map(([id, session]) => ({
        id,
        createdAt: this.formatDateForChina(session.createdAt),
        lastActivity: this.formatDateForChina(session.lastActivity),
        messages: session.messages.map(msg => ({
          type: msg.constructor.name,
          content: msg.content,
          additional_kwargs: msg.additional_kwargs,
        })),
        userMemory: session.userMemory,
        metadata: session.metadata,
      }));

      await fs.writeFile(this.sessionsFilePath, JSON.stringify(sessionsData, null, 2), 'utf-8');
      logger.debug(`[SessionManager] Saved ${sessionsData.length} sessions to disk`);
    } catch (error) {
      logger.error('[SessionManager] Failed to save sessions to disk:', error);
    }
  }

  /**
   * 从字符串解析日期（支持中国时区格式和ISO格式）
   */
  private parseDateFromString(dateString: string): Date {
    // 检查是否为中国时区格式
    if (dateString.includes('(中国时间)')) {
      // 提取日期时间部分: "YYYY-MM-DD HH:mm:ss"
      const dateTimePart = dateString.split(' (中国时间)')[0];
      // 使用 dayjs 解析中国时区的时间并转换为 UTC
      return dayjs.tz(dateTimePart, 'Asia/Shanghai').toDate();
    }
    
    // 兼容标准 ISO 字符串格式
    return dayjs(dateString).toDate();
  }

  /**
   * Load sessions from disk
   */
  private async loadSessionsFromDisk(): Promise<void> {
    if (!this.persistenceEnabled) {
      return;
    }

    try {
      const data = await fs.readFile(this.sessionsFilePath, 'utf-8');
      const sessionsData = JSON.parse(data);

      if (!Array.isArray(sessionsData)) {
        logger.warning('[SessionManager] Invalid sessions data format, starting fresh');
        return;
      }

      for (const sessionData of sessionsData) {
        try {
          // Reconstruct messages including tool messages
          const messages: BaseMessage[] = sessionData.messages.map((msgData: Record<string, unknown>) => {
            switch (msgData.type) {
              case 'HumanMessage':
                return new HumanMessage(String(msgData.content));
              case 'AIMessage': {
                // Reconstruct AIMessage with tool calls if present
                const aiMessage = new AIMessage(String(msgData.content));
                if (msgData.tool_calls && Array.isArray(msgData.tool_calls)) {
                  aiMessage.tool_calls = msgData.tool_calls as Array<{
                    id: string;
                    name: string;
                    args: Record<string, unknown>;
                  }>;
                }
                return aiMessage;
              }
              case 'ToolMessage':
                // Reconstruct ToolMessage
                return new ToolMessage({
                  content: String(msgData.content),
                  tool_call_id: String(msgData.tool_call_id || ''),
                });
              default:
                return new HumanMessage(String(msgData.content)); // fallback
            }
          });

          const session: SessionData = {
            id: sessionData.id,
            createdAt: this.parseDateFromString(sessionData.createdAt),
            lastActivity: this.parseDateFromString(sessionData.lastActivity),
            messages,
            userMemory: sessionData.userMemory,
            metadata: sessionData.metadata || {},
          };

          this.sessions.set(session.id, session);

          // Initialize logger for this session
          const sessionLogger = new HaicodeLogger(session.id);
          await sessionLogger.initialize();
          this.loggers.set(session.id, sessionLogger);

        } catch (error) {
          logger.error(`[SessionManager] Failed to restore session ${sessionData.id}:`, error);
        }
      }

      logger.debug(`[SessionManager] Loaded ${this.sessions.size} sessions from disk`);
    } catch (error) {
      if (error instanceof Error && 'code' in error && error.code === 'ENOENT') {
        logger.debug('[SessionManager] No existing sessions file found, starting fresh');
      } else {
        logger.error('[SessionManager] Failed to load sessions from disk:', error);
      }
    }
  }

  /**
   * Save a checkpoint for a session
   */
  async saveCheckpoint(sessionId: string, tag: string): Promise<boolean> {
    if (!this.persistenceEnabled || !this.loggers.has(sessionId)) {
      return false;
    }

    const session = this.sessions.get(sessionId);
    if (!session) {
      return false;
    }

    try {
      const sessionLogger = this.loggers.get(sessionId)!;
      await sessionLogger.saveCheckpoint(session.messages, tag);
      logger.debug(`[SessionManager] Saved checkpoint '${tag}' for session ${sessionId}`);
      return true;
    } catch (error) {
      logger.error(`[SessionManager] Failed to save checkpoint '${tag}' for session ${sessionId}:`, error);
      return false;
    }
  }

  /**
   * Load a checkpoint for a session
   */
  async loadCheckpoint(sessionId: string, tag: string): Promise<boolean> {
    if (!this.persistenceEnabled || !this.loggers.has(sessionId)) {
      return false;
    }

    const session = this.sessions.get(sessionId);
    if (!session) {
      return false;
    }

    try {
      const sessionLogger = this.loggers.get(sessionId)!;
      const messages = await sessionLogger.loadCheckpoint(tag);
      
      if (messages.length > 0) {
        session.messages = messages;
        session.lastActivity = new Date();
        
        // Save the updated session to disk
        await this.saveSessionsToDisk();
        
        logger.debug(`[SessionManager] Loaded checkpoint '${tag}' for session ${sessionId}`);
        return true;
      }
      
      return false;
    } catch (error) {
      logger.error(`[SessionManager] Failed to load checkpoint '${tag}' for session ${sessionId}:`, error);
      return false;
    }
  }

  /**
   * Get session logger for external use
   */
  getSessionLogger(sessionId: string): HaicodeLogger | undefined {
    return this.loggers.get(sessionId);
  }

  /**
   * Enable or disable persistence
   */
  setPersistenceEnabled(enabled: boolean): void {
    this.persistenceEnabled = enabled;
    
    if (!enabled) {
      // Clear all loggers when disabling persistence
      this.loggers.clear();
    } else if (enabled && this.sessions.size > 0) {
      // Initialize loggers for existing sessions when enabling persistence
      this.sessions.forEach((_session, sessionId) => {
        const sessionLogger = new HaicodeLogger(sessionId);
        sessionLogger.initialize().catch(error => {
          logger.error(`[SessionManager] Failed to initialize logger for session ${sessionId}:`, error);
        });
        this.loggers.set(sessionId, sessionLogger);
      });
    }
  }
}