#!/usr/bin/env node

/**
 * 测试所有修复的功能
 */

import { createLangChainGeminiCLI } from './dist/index.js';
import fs from 'node:fs';
import path from 'node:path';
import os from 'os';

async function testAllFixes() {
  console.log('🧪 测试所有修复的功能...\n');
  
  // 捕获console.warn输出
  const warnings = [];
  const originalWarn = console.warn;
  console.warn = (...args) => {
    warnings.push(args.join(' '));
    originalWarn(...args);
  };
  
  try {
    const targetDir = process.cwd();
    
    // 创建CLI实例
    const cli = await createLangChainGeminiCLI({
      sessionId: 'test-all-fixes',
      model: 'ht::saas-deepseek-v3',
      authType: 'USE_OPENAI_COMPATIBLE',
      targetDir,
      cwd: targetDir,
      debugMode: true,
      coreTools: ['list_directory', 'read_file'],
    });

    console.log('✅ CLI实例创建成功');
    
    // 恢复原始console.warn
    console.warn = originalWarn;
    
    // 1. 测试Zod警告抑制
    const zodWarnings = warnings.filter(warning => 
      warning.includes('Zod field') && 
      warning.includes('.optional()') && 
      warning.includes('without .nullable()')
    );
    
    console.log(`\n📊 Zod警告测试:`);
    console.log(`Zod相关警告: ${zodWarnings.length}`);
    if (zodWarnings.length === 0) {
      console.log('✅ Zod警告已成功抑制');
    } else {
      console.log('❌ Zod警告仍然存在');
      zodWarnings.forEach(w => console.log(`  - ${w.substring(0, 100)}...`));
    }
    
    // 2. 测试工具描述信息
    const tools = cli.config.toolRegistry.getLangChainTools();
    console.log(`\n🔧 工具描述测试:`);
    console.log(`已注册工具数量: ${tools.length}`);
    
    if (tools.length > 0) {
      const firstTool = tools[0];
      const hasParams = firstTool.description.includes('**Parameters:**');
      console.log(`工具名: ${firstTool.name}`);
      console.log(`包含参数信息: ${hasParams ? '✅' : '❌'}`);
      if (hasParams) {
        console.log('✅ 工具描述信息完整');
      } else {
        console.log('❌ 工具描述信息缺失');
      }
    }
    
    // 3. 测试会话信息同步到.haicode目录
    console.log(`\n💾 会话同步测试:`);
    
    // 计算预期的.haicode路径
    const projectHash = encodeURIComponent(targetDir.replace(/\//g, '__'));
    const expectedHaicodeDir = path.join(os.homedir(), '.haicode', 'tmp', projectHash);
    const expectedSessionsFile = path.join(expectedHaicodeDir, 'sessions.json');
    
    console.log(`预期.haicode目录: ${expectedHaicodeDir}`);
    console.log(`预期sessions文件: ${expectedSessionsFile}`);
    
    // 创建一个测试会话
    const testResponse = await cli.processMessage('你好');
    console.log(`测试消息响应: ${testResponse.substring(0, 50)}...`);
    
    // 等待一下让文件写入完成
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 检查文件是否存在
    const haicodeExists = fs.existsSync(expectedHaicodeDir);
    const sessionsExists = fs.existsSync(expectedSessionsFile);
    
    console.log(`.haicode目录存在: ${haicodeExists ? '✅' : '❌'}`);
    console.log(`sessions.json存在: ${sessionsExists ? '✅' : '❌'}`);
    
    if (sessionsExists) {
      try {
        const sessionsContent = fs.readFileSync(expectedSessionsFile, 'utf8');
        const sessions = JSON.parse(sessionsContent);
        console.log(`会话数量: ${sessions.length}`);
        console.log('✅ 会话信息已同步到.haicode目录');
      } catch (error) {
        console.log('❌ 会话文件格式错误:', error.message);
      }
    } else {
      console.log('❌ 会话信息未同步到.haicode目录');
    }
    
    // 4. 测试AIMessageChunk流式输出
    console.log(`\n🌊 流式输出测试:`);
    let streamChunks = 0;
    let streamContent = '';
    
    console.log('开始流式测试...');
    for await (const chunk of cli.streamMessage('请简单说一句话')) {
      streamChunks++;
      streamContent += chunk;
      if (streamChunks <= 3) {
        console.log(`流式块 ${streamChunks}: "${chunk}"`);
      }
    }
    
    console.log(`总流式块数: ${streamChunks}`);
    console.log(`流式内容长度: ${streamContent.length}`);
    console.log(`流式内容预览: ${streamContent.substring(0, 50)}...`);
    
    if (streamChunks > 0 && streamContent.length > 0) {
      console.log('✅ AIMessageChunk流式输出正常');
    } else {
      console.log('❌ AIMessageChunk流式输出异常');
    }
    
    console.log('\n🎉 所有测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

testAllFixes().catch(console.error);
