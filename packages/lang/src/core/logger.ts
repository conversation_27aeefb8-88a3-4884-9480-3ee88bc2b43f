/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import path from 'node:path';
import { promises as fs } from 'node:fs';
import { BaseMessage } from '@langchain/core/messages';
import { getProjectTempDir } from '../utils/paths.js';
import { logger as log } from '../utils/logger.js';

const LOG_FILE_NAME = 'logs.json';

export enum MessageSenderType {
  USER = 'user',
  ASSISTANT = 'assistant',
}

export interface LogEntry {
  sessionId: string;
  messageId: number;
  timestamp: string;
  type: MessageSenderType;
  message: string;
  metadata?: Record<string, unknown>;
}

export class HaicodeLogger {
  private haicodeDir: string | undefined;
  private logFilePath: string | undefined;
  private sessionId: string | undefined;
  private messageId = 0; // Instance-specific counter for the next messageId
  private initialized = false;
  private logs: LogEntry[] = []; // In-memory cache, ideally reflects the last known state of the file

  constructor(sessionId: string) {
    this.sessionId = sessionId;
  }

  private async _readLogFile(): Promise<LogEntry[]> {
    if (!this.logFilePath) {
      throw new Error('Log file path not set during read attempt.');
    }
    try {
      const fileContent = await fs.readFile(this.logFilePath, 'utf-8');
      const parsedLogs = JSON.parse(fileContent);
      if (!Array.isArray(parsedLogs)) {
        log.debug(
          `Log file at ${this.logFilePath} is not a valid JSON array. Starting with empty logs.`,
        );
        await this._backupCorruptedLogFile('malformed_array');
        return [];
      }
      return parsedLogs;
    } catch (error: unknown) {
      if (error instanceof Error && 'code' in error && error.code === 'ENOENT') {
        // File doesn't exist yet, which is fine - return empty array
        return [];
      }
      log.debug('Error reading log file:', error);
      if (error instanceof SyntaxError) {
        await this._backupCorruptedLogFile('syntax_error');
        return [];
      }
      throw error;
    }
  }

  private async _backupCorruptedLogFile(reason: string): Promise<void> {
    if (!this.logFilePath) {
      return;
    }
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupPath = `${this.logFilePath}.backup.${reason}.${timestamp}`;
      await fs.copyFile(this.logFilePath, backupPath);
      log.debug(`Backed up corrupted log file to: ${backupPath}`);
    } catch (backupError) {
      log.debug('Failed to backup corrupted log file:', backupError);
    }
  }

  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    this.haicodeDir = getProjectTempDir(process.cwd());
    this.logFilePath = path.join(this.haicodeDir, LOG_FILE_NAME);

    try {
      await fs.mkdir(this.haicodeDir, { recursive: true });
      let fileExisted = true;
      try {
        await fs.access(this.logFilePath);
      } catch (_e) {
        fileExisted = false;
      }
      this.logs = await this._readLogFile();
      if (!fileExisted && this.logs.length === 0) {
        await fs.writeFile(this.logFilePath, '[]', 'utf-8');
      }
      const sessionLogs = this.logs.filter(
        (entry) => entry.sessionId === this.sessionId,
      );
      this.messageId =
        sessionLogs.length > 0
          ? Math.max(...sessionLogs.map((entry) => entry.messageId)) + 1
          : 0;
      this.initialized = true;
    } catch (err) {
      log.error('Failed to initialize haicode logger:', err);
      this.initialized = false;
    }
  }

  private async _updateLogFile(
    entryToAppend: LogEntry,
  ): Promise<LogEntry | null> {
    if (!this.logFilePath) {
      log.debug('Log file path not set. Cannot persist log entry.');
      throw new Error('Log file path not set during update attempt.');
    }

    let currentLogsOnDisk: LogEntry[];
    try {
      currentLogsOnDisk = await this._readLogFile();
    } catch (readError) {
      log.debug(
        'Critical error reading log file before append:',
        readError,
      );
      throw readError;
    }

    // Determine the correct messageId for the new entry based on current disk state for its session
    const sessionLogsOnDisk = currentLogsOnDisk.filter(
      (e) => e.sessionId === entryToAppend.sessionId,
    );
    const nextMessageIdForSession =
      sessionLogsOnDisk.length > 0
        ? Math.max(...sessionLogsOnDisk.map((e) => e.messageId)) + 1
        : 0;

    // Update the messageId of the entry we are about to append
    entryToAppend.messageId = nextMessageIdForSession;

    // Check if this entry (same session, same content, same type) might already exist
    // We check for content and type similarity rather than exact timestamp match
    // to catch true duplicates even if they have slightly different timestamps
    const entryExists = currentLogsOnDisk.some(
      (e) =>
        e.sessionId === entryToAppend.sessionId &&
        e.type === entryToAppend.type &&
        e.message === entryToAppend.message &&
        // Check if metadata is similar (for tool calls, etc.)
        JSON.stringify(e.metadata || {}) === JSON.stringify(entryToAppend.metadata || {}),
    );

    if (entryExists) {
      log.debug(
        `Duplicate log entry detected and skipped: session ${entryToAppend.sessionId}, messageId ${entryToAppend.messageId}`,
      );
      this.logs = currentLogsOnDisk; // Ensure in-memory is synced with disk
      return null; // Indicate that no new entry was actually added
    }

    currentLogsOnDisk.push(entryToAppend);

    try {
      await fs.writeFile(
        this.logFilePath,
        JSON.stringify(currentLogsOnDisk, null, 2),
        'utf-8',
      );

      // Update our in-memory cache with the new disk state
      this.logs = currentLogsOnDisk;

      // Update the instance messageId to the next available for this session
      this.messageId = nextMessageIdForSession + 1;

      return entryToAppend; // Return the successfully appended entry
    } catch (writeError) {
      log.debug('Error writing to log file:', writeError);
      throw writeError;
    }
  }

  async log(message: string, type: MessageSenderType = MessageSenderType.USER, metadata?: Record<string, unknown>): Promise<void> {
    if (!this.initialized) {
      log.error(
        'Haicode logger not initialized. Cannot log message.',
      );
      return;
    }

    const entry: LogEntry = {
      sessionId: this.sessionId!,
      messageId: this.messageId, // This will be updated by _updateLogFile
      timestamp: new Date().toISOString(),
      type,
      message,
      metadata,
    };

    try {
      await this._updateLogFile(entry);
    } catch (error) {
      log.error('Failed to log message:', error);
    }
  }

  private _checkpointPath(tag: string): string {
    if (!tag.length) {
      throw new Error('No checkpoint tag specified.');
    }
    if (!this.haicodeDir) {
      throw new Error('Checkpoint file path not set.');
    }
    // Sanitize tag to prevent directory traversal attacks (same as core Logger)
    tag = tag.replace(/[^a-zA-Z0-9-_]/g, '');
    if (!tag) {
      log.error('Sanitized tag is empty setting to "default".');
      tag = 'default';
    }
    return path.join(this.haicodeDir, `checkpoint-${tag}.json`);
  }

  async saveCheckpoint(conversation: BaseMessage[], tag: string): Promise<void> {
    if (!this.initialized) {
      log.error(
        'Logger not initialized or checkpoint file path not set. Cannot save a checkpoint.',
      );
      return;
    }
    const checkpointPath = this._checkpointPath(tag);
    try {
      // Convert BaseMessage to serializable format compatible with core logger
      const serializedConversation = conversation.map(msg => ({
        role: msg.constructor.name === 'HumanMessage' ? 'user' : 'model',
        parts: [{ text: String(msg.content) }],
        // Keep additional data for compatibility
        _langchain_type: msg.constructor.name,
        _additional_kwargs: msg.additional_kwargs,
      }));
      await fs.writeFile(checkpointPath, JSON.stringify(serializedConversation, null, 2), 'utf-8');
      log.debug(`Saved checkpoint '${tag}' to ${checkpointPath}`);
    } catch (error) {
      log.error('Error writing to checkpoint file:', error);
    }
  }

  async loadCheckpoint(tag: string): Promise<BaseMessage[]> {
    if (!this.initialized) {
      log.error(
        'Logger not initialized or checkpoint file path not set. Cannot load checkpoint.',
      );
      return [];
    }
    const checkpointPath = this._checkpointPath(tag);
    try {
      const fileContent = await fs.readFile(checkpointPath, 'utf-8');
      const parsedContent = JSON.parse(fileContent);
      if (!Array.isArray(parsedContent)) {
        log.warning(
          `Checkpoint file at ${checkpointPath} is not a valid JSON array. Returning empty checkpoint.`,
        );
        return [];
      }
      
      // Convert back from core logger format to LangChain BaseMessage
      const { HumanMessage, AIMessage, ToolMessage } = await import('@langchain/core/messages');

      return parsedContent.map((msgData: Record<string, unknown>) => {
        // Safely extract content from different formats
        const parts = msgData.parts as Array<{ text?: string }> | undefined;
        const content = parts?.[0]?.text || String(msgData.content || '');
        const additionalKwargs = (msgData._additional_kwargs || msgData.additional_kwargs || {}) as Record<string, unknown>;

        // Determine message type based on role or langchain type
        if (msgData.role === 'user' || msgData._langchain_type === 'HumanMessage') {
          return new HumanMessage({ content, additional_kwargs: additionalKwargs });
        } else if (msgData._langchain_type === 'ToolMessage') {
          // Reconstruct ToolMessage
          return new ToolMessage({
            content,
            tool_call_id: String(msgData.tool_call_id || ''),
            additional_kwargs: additionalKwargs,
          });
        } else {
          // AIMessage - reconstruct with tool calls if present
          const aiMessage = new AIMessage({ content, additional_kwargs: additionalKwargs });
          if (msgData.tool_calls && Array.isArray(msgData.tool_calls)) {
            aiMessage.tool_calls = msgData.tool_calls as Array<{
              id: string;
              name: string;
              args: Record<string, unknown>;
            }>;
          }
          return aiMessage;
        }
      });
    } catch (error) {
      log.error(`Failed to read or parse checkpoint file ${checkpointPath}:`, error);
      const nodeError = error as NodeJS.ErrnoException;
      if (nodeError.code === 'ENOENT') {
        // File doesn't exist, which is fine. Return empty array.
        return [];
      }
      return [];
    }
  }

  getSessionLogs(): LogEntry[] {
    return this.logs.filter(entry => entry.sessionId === this.sessionId);
  }

  getAllLogs(): LogEntry[] {
    return [...this.logs];
  }
}