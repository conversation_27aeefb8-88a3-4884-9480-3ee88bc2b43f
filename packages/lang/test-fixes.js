#!/usr/bin/env node

/**
 * 测试修复后的功能
 */

import { createLangChainGeminiCLI } from './dist/index.js';

async function testFixes() {
  console.log('🧪 测试修复后的功能...\n');
  
  try {
    // 创建CLI实例
    const cli = await createLangChainGeminiCLI({
      sessionId: 'test-session',
      model: 'ht::saas-deepseek-v3',
      authType: 'USE_OPENAI_COMPATIBLE',
      targetDir: process.cwd(),
      cwd: process.cwd(),
      debugMode: true,
      coreTools: [
        'list_directory',
        'read_file', 
        'search_file_content',
        'replace',
      ],
    });

    console.log('✅ CLI实例创建成功');
    
    // 测试工具描述信息
    console.log('\n📋 测试工具描述信息:');
    const tools = cli.config.toolRegistry.getLangChainTools();
    for (const tool of tools.slice(0, 2)) { // 只显示前两个工具
      console.log(`\n🔧 工具: ${tool.name}`);
      console.log(`📝 描述: ${tool.description.substring(0, 200)}...`);
    }
    
    // 测试简单对话
    console.log('\n💬 测试简单对话:');
    const response = await cli.processMessage('你好，请简单介绍一下你自己');
    console.log(`🤖 AI回复: ${response.substring(0, 100)}...`);
    
    console.log('\n✅ 所有测试通过！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

testFixes().catch(console.error);
